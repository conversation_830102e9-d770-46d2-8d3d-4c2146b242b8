import { prepararDocumentos } from '../gemini/geminiClient';
import { 
  PROMPT_RESUMEN_CHUNK_EXPANSION, 
  PROMPT_RESUMEN_FINAL_CONSOLIDACION,
  generateChunkingContext 
} from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Documento } from '@/types';
import { type Chunk } from '@/types/chunking';

/**
 * Orquesta la generación de resúmenes jerárquicos utilizando un enfoque Map-Reduce.
 */
export class SummaryOrchestrator {

  /**
   * Procesa un único chunk para expandir su contenido. (Fase "Map")
   * @param chunk El fragmento de texto a procesar.
   * @param chunkingContext El contexto general del chunking.
   * @returns El contenido expandido del chunk.
   */
  private static async expandChunk(chunk: Chunk, chunkingContext: any): Promise<string> {
    const contextString = generateChunkingContext(chunkingContext);

    const prompt = PROMPT_RESUMEN_CHUNK_EXPANSION
      .replace('{chunking_context}', contextString)
      .replace('{chunk_content}', chunk.content);

    const config = getOpenAIConfig('RESUMENES'); // Usar la misma config que resúmenes
    const messages = [{ role: 'user' as const, content: prompt }];
    
    console.log(`🗺️ [Map] Expandiendo chunk ${chunk.metadata.chunkNumber}...`);
    
    return llamarOpenAI(messages, {
      ...config,
      activityName: `Resumen-Map (Chunk ${chunk.metadata.chunkNumber})`
    });
  }

  /**
   * Genera un resumen de alta calidad para un documento, usando Map-Reduce si es necesario.
   * @param documento El documento a resumir.
   * @param instrucciones Instrucciones adicionales del usuario.
   * @param userId ID del usuario para tracking de tokens.
   * @returns El resumen final y pulido.
   */
  public static async generateHierarchicalSummary(
    documento: Documento,
    _instrucciones?: string,
    userId?: string
  ): Promise<string> {
    console.log(`🚀 Iniciando generación de resumen jerárquico para: ${documento.titulo}`);
    console.log(`🔍 [USERID_TRACE] SummaryOrchestrator recibió userId: ${userId}`);

    // 1. Preparar documentos y obtener chunks
    const documentoParaProcesar = {
      titulo: documento.titulo,
      contenido: documento.contenido || documento.contenido_corto || '',
      categoria: documento.categoria,
      numero_tema: documento.numero_tema
    };
    const resultadoDocumentos = prepararDocumentos([documentoParaProcesar]);

    if (!resultadoDocumentos.wasChunked || !Array.isArray(resultadoDocumentos.content) || resultadoDocumentos.content.length === 0) {
      throw new Error("El documento no pudo ser dividido en chunks para el procesamiento jerárquico.");
    }

    const chunks = resultadoDocumentos.content as Chunk[];
    console.log(`📄 Documento dividido en ${chunks.length} chunks.`);

    // 2. Fase "Map": Expandir cada chunk en paralelo
    const chunkingGlobalContext = {
      totalChunks: chunks.length,
      documentSections: chunks.length > 0 ? chunks[0].metadata.detectedSections : [],
      chunkingStrategy: 'default' // O el que se use en prepararDocumentos
    };

    const expansionPromises = chunks.map((chunk, index) => {
      const chunkContext = {
        ...chunkingGlobalContext,
        currentChunk: index + 1,
        hasPreviousChunks: index > 0,
        hasNextChunks: index < chunks.length - 1,
      };
      return this.expandChunk(chunk, chunkContext);
    });

    const expandedContents = await Promise.all(expansionPromises);
    console.log('✅ [Map] Todos los chunks han sido expandidos.');

    // 3. Fase "Reduce": Consolidar los contenidos expandidos
    const texto_largo_combinado = expandedContents.join('\n\n---\n\n');
    console.log(`📝 [Reduce] Combinando ${expandedContents.length} expansiones en un solo documento de ${texto_largo_combinado.length} caracteres.`);

    const finalPrompt = PROMPT_RESUMEN_FINAL_CONSOLIDACION
      .replace('{texto_largo_combinado}', texto_largo_combinado);

    const config = getOpenAIConfig('RESUMENES'); // Reutilizar la config de resúmenes para la fase de consolidación
    const messages = [{ role: 'user' as const, content: finalPrompt }];

    console.log('✍️ [Reduce] Solicitando consolidación y edición final a la IA...');
    console.log(`🔍 [USERID_TRACE] Fase REDUCE - userId antes de llamarOpenAI: ${userId}`);
    const finalResult = await llamarOpenAI(messages, {
      ...config,
      activityName: 'Resumen-Reduce (Consolidación Final)',
      userId
    });

    console.log('🎉 Resumen jerárquico generado exitosamente.');
    return finalResult;
  }
}
