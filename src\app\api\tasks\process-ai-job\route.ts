import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase/admin';
import { generarTest, generarFlashcards, generarMapaMental, generarResumen } from '@/lib/gemini';
import { generarPlanEstudios } from '@/features/planificacion/services/planGeneratorService';
import { downloadFileContentFromServer } from '@/lib/supabase/storageService.server';
import { User } from '@supabase/supabase-js';
import * as Sentry from "@sentry/nextjs";

// Configuración requerida para funciones de larga duración en Vercel
export const dynamic = 'force-dynamic';

// ¡IMPORTANTE! Protege este endpoint con una clave secreta.
const CRON_SECRET = process.env.CRON_SECRET;

/**
 * Reconstruye el contenido de los documentos a partir de sus referencias
 * Si los documentos ya tienen contenido, los devuelve tal como están
 * Si solo tienen referencias (storage_path), descarga el contenido
 */
async function reconstructDocumentContent(documentos: any[]): Promise<any[]> {
  if (!documentos || documentos.length === 0) {
    return [];
  }

  // Si es un array de strings (contextos directos), devolverlos tal como están
  if (typeof documentos[0] === 'string') {
    return documentos;
  }

  // Si los documentos ya tienen contenido, devolverlos tal como están
  if (documentos[0].contenido) {
    return documentos;
  }

  // Si solo tienen referencias, descargar el contenido
  console.log(`📥 [AI_WORKER] Descargando contenido de ${documentos.length} documentos...`);

  const documentosConContenido = [];
  for (const doc of documentos) {
    if (doc.storage_path) {
      try {
        const contenido = await downloadFileContentFromServer(doc.storage_path);
        documentosConContenido.push({
          ...doc,
          contenido
        });
      } catch (error) {
        console.error(`❌ [AI_WORKER] Error al descargar documento ${doc.titulo}:`, error);
        throw new Error(`No se pudo descargar el contenido del documento: ${doc.titulo}`);
      }
    } else {
      // Si no tiene storage_path, mantener el documento tal como está
      documentosConContenido.push(doc);
    }
  }

  console.log(`✅ [AI_WORKER] Contenido descargado exitosamente para ${documentosConContenido.length} documentos`);
  return documentosConContenido;
}

export async function POST(request: NextRequest) {
  try {
    // 1. Verificar la clave secreta
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${CRON_SECRET}`) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { taskId } = await request.json();
    if (!taskId) {
      return NextResponse.json({ error: 'taskId es requerido' }, { status: 400 });
    }

    console.log(`🔄 [AI_WORKER] Procesando tarea: ${taskId}`);

    // 2. Marcar la tarea como 'processing'
    await supabaseAdmin.from('ai_tasks').update({
      status: 'processing',
      started_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }).eq('id', taskId);

    // 3. Obtener los datos de la tarea
    const { data: task, error: taskError } = await supabaseAdmin
      .from('ai_tasks').select('*').eq('id', taskId).single();

    if (taskError || !task) {
      throw new Error(`Tarea con ID ${taskId} no encontrada.`);
    }

    console.log(`📋 [AI_WORKER] Tarea obtenida: ${task.type}`);

    // 4. Ejecutar la lógica de generación de IA correspondiente
    let result;
    const input = task.input_data;
    
    switch (task.type) {
      case 'test':
        console.log(`🧪 [AI_WORKER] Generando test con ${input.cantidad} preguntas`);
        const documentosTest = await reconstructDocumentContent(input.documentos);
        result = await generarTest(input.peticion, documentosTest, input.cantidad);
        break;

      case 'flashcards':
        console.log(`🃏 [AI_WORKER] Generando ${input.cantidad} flashcards`);
        const documentosFlashcards = await reconstructDocumentContent(input.documentos);
        result = await generarFlashcards(input.peticion, documentosFlashcards, input.cantidad);
        break;

      case 'mindmap':
        console.log(`🗺️ [AI_WORKER] Generando mapa mental`);
        const documentosMapaMental = await reconstructDocumentContent(input.documentos);
        result = await generarMapaMental(input.peticion, documentosMapaMental);
        break;
        
      case 'summary':
        console.log(`📄 [AI_WORKER] Generando resumen`);
        // Para resumen, necesitamos reconstruir el contenido del documento
        // Si el documento no tiene contenido, necesitamos descargarlo
        let documentoCompleto = input.documento;
        if (!documentoCompleto.contenido && documentoCompleto.storage_path) {
          console.log(`📥 [AI_WORKER] Descargando contenido para resumen...`);
          const contenido = await downloadFileContentFromServer(documentoCompleto.storage_path);
          documentoCompleto = {
            ...documentoCompleto,
            contenido
          };
        }
        result = await generarResumen(documentoCompleto, input.instrucciones);
        break;
        
      case 'plan':
        console.log(`📅 [AI_WORKER] Generando plan de estudios`);
        // Para generar el plan necesitamos el objeto de usuario
        const { data: { user } } = await supabaseAdmin.auth.admin.getUserById(task.user_id);
        if (!user) throw new Error('Usuario no encontrado para generar plan');
        result = await generarPlanEstudios(input.temarioId, user as User);
        break;
        
      default:
        throw new Error(`Tipo de tarea desconocido: ${task.type}`);
    }

    console.log(`✅ [AI_WORKER] Tarea ${taskId} completada exitosamente`);

    // 5. Guardar el resultado y marcar como 'completed'
    await supabaseAdmin.from('ai_tasks').update({
      status: 'completed',
      result: result,
      completed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }).eq('id', taskId);

    return NextResponse.json({ 
      success: true, 
      message: `Tarea ${taskId} completada.`,
      type: task.type
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    console.error(`❌ [AI_WORKER] Error procesando tarea:`, errorMessage);

    // Capturar error en Sentry
    Sentry.captureException(error, {
      tags: { section: "ai-worker" },
      extra: {
        context: "Error processing AI task",
        taskId: request.url,
        timestamp: new Date().toISOString()
      },
    });

    // 6. En caso de error, guardarlo y marcar como 'error'
    try {
      const { taskId } = await request.json();
      if (taskId) {
        await supabaseAdmin.from('ai_tasks').update({
          status: 'error',
          error_message: errorMessage,
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }).eq('id', taskId);
      }
    } catch (updateError) {
      console.error(`❌ [AI_WORKER] Error actualizando estado de error:`, updateError);
    }

    return NextResponse.json({ 
      success: false, 
      error: errorMessage 
    }, { status: 500 });
  }
}
